/**
 * TypeScript types for react-i18next translation keys.
 * This ensures type safety when using translation keys.
 */

// Define the structure of our translation resources
export interface TranslationResources {
  auth: {
    login: {
      title: string;
      description: string;
      email: string;
      password: string;
      forgotPassword: string;
      submit: string;
      submitting: string;
      error: string;
      noAccount: string;
      signUp: string;
    };
    register: {
      title: string;
      description: string;
      accountName: string;
      displayName: string;
      email: string;
      password: string;
      passwordHint: string;
      submit: string;
      submitting: string;
      error: string;
      hasAccount: string;
      signIn: string;
      success: {
        title: string;
        description: string;
        checkEmail: string;
        registerAnother: string;
        goToLogin: string;
      };
    };
    initializing: string;
    verifying: string;
  };
  validation: {
    required: string;
    email: {
      invalid: string;
      tooLong: string;
    };
    password: {
      tooShort: string;
      tooLong: string;
      requirements: string;
    };
    accountName: {
      tooShort: string;
      tooLong: string;
    };
    displayName: {
      required: string;
      tooLong: string;
    };
  };
  placeholders: {
    accountName: string;
    displayName: string;
    email: string;
    password: string;
  };
  messages: {
    emailSentTo: string;
  };
  accountCompanies: {
    title: string;
    description: string;
    createNew: string;
    noCompanies: string;
    noCompaniesDescription: string;
    loading: string;
    error: string;
    edit: string;
    delete: string;
    confirmDelete: string;
    deleteDescription: string;
    deleting: string;
    creating: string;
    updating: string;
    createTitle: string;
    editTitle: string;
    save: string;
    cancel: string;
    companyName: string;
    email: string;
    phone: string;
    website: string;
    addressStreet: string;
    addressCity: string;
    addressPostalCode: string;
    addressCountry: string;
    vatNumber: string;
    registrationNumber: string;
    required: string;
    validation: {
      companyNameRequired: string;
      companyNameTooLong: string;
      emailInvalid: string;
      emailTooLong: string;
      phoneTooLong: string;
      websiteTooLong: string;
      addressStreetTooLong: string;
      addressCityTooLong: string;
      addressPostalCodeTooLong: string;
      addressCountryTooLong: string;
      vatNumberTooLong: string;
      registrationNumberTooLong: string;
    };
  };
  bankDetails: {
    title: string;
    description: string;
    createNew: string;
    noBankDetails: string;
    noBankDetailsDescription: string;
    loading: string;
    error: string;
    edit: string;
    delete: string;
    confirmDelete: string;
    deleteDescription: string;
    deleting: string;
    creating: string;
    updating: string;
    createTitle: string;
    editTitle: string;
    save: string;
    cancel: string;
    name: string;
    bankName: string;
    iban: string;
    bicSwift: string;
    required: string;
    validation: {
      nameRequired: string;
      nameTooLong: string;
      bankNameTooLong: string;
      ibanInvalid: string;
      ibanTooShort: string;
      ibanTooLong: string;
      bicSwiftInvalid: string;
      bicSwiftTooShort: string;
      bicSwiftTooLong: string;
    };
  };
  invoices: {
    // Page content
    title: string;
    description: string;
    createNew: string;
    loading: string;
    error: string;
    noInvoices: string;
    noInvoicesDescription: string;

    // Status labels
    daysOverdue: string;
    dueIn: string;
    dueToday: string;

    // Common fields
    invoiceNumber: string;
    recipient: string;
    issueDate: string;
    dueDate: string;

    // Actions
    viewDetails: string;
    edit: string;
    send: string;
    download: string;
    delete: string;
    cancel: string;

    // Status labels
    primary: string;
    cc: string;

    // Loading states
    sending: string;
    deleting: string;

    // Totals
    subtotal: string;
    totalVat: string;
    totalAmount: string;

    // Filters
    filters: {
      title: string;
      status: string;
      dateRange: string;
      fromDate: string;
      toDate: string;
      clearAll: string;
      apply: string;
      activeFilters: string;
    };

    // Status
    status: {
      draft: string;
      sent: string;
      paid: string;
      overdue: string;
    };

    // Status update dialog
    statusUpdate: {
      title: string;
      currentStatus: string;
      newStatus: string;
      note: string;
      update: string;
      updating: string;
    };

    // Send dialog
    sendDialog: {
      title: string;
      description: string;
      recipientsList: string;
      confirmSend: string;
      success: string;
      error: string;
    };

    // Send confirmation
    sendConfirmation: {
      confirmSendTitle: string;
      confirmSendDescription: string;
      confirmResendTitle: string;
      confirmResendDescription: string;
      alreadySentWarning: string;
      send: string;
      forceSend: string;
      cancel: string;
    };

    viewDialog: {
      title: string;
      basicInfo: string;
      lineItems: string;
      noLineItems: string;
      recipients: string;
      totals: string;
      entityIds: string;
      issuerInfo: string;
      recipientInfo: string;
      bankDetailsInfo: string;
      loading: string;
      error: string;
      close: string;
      downloadPdf: string;
      companyName: string;
      name: string;
      address: string;
      contacts: string;
      accountName: string;
      bankName: string;
      vatAmount: string;
      recipientLabels: {
        type: {
          original: string;
          copy: string;
        };
        source: {
          brand_contact: string;
          manual: string;
        };
        sendCount: string;
        lastSent: string;
        noRecipients: string;
      };
    };

    // Form section
    form: {
      basicInfo: string;
      clientInfo: string;
      lineItems: string;
      recipients: string;
      totals: string;
      additionalDetails: string;
      invoiceNumber: string;
      currency: string;
      issueDate: string;
      dueDate: string;
      issuer: string;
      recipient: string;
      bankDetails: string;
      myBankDetails: string;
      notes: string;
      noIssuersFound: string;
      noBrandsFound: string;
      noBankDetailsFound: string;
      optional: string;
      description: string;
      quantity: string;
      unitPrice: string;
      vatRate: string;
      lineTotal: string;
      lineItem: string;
      addItem: string;
      email: string;
      recipientType: string;
      addRecipient: string;
      createTitle: string;
      editTitle: string;
      createInvoice: string;
      updateInvoice: string;
      creating: string;
      updating: string;
      cancel: string;
      close: string;
      readOnlyMessage: string;
      loadingInvoice: string;
      error: string;
      generateInvoiceNumber: string;
      generatingInvoiceNumber: string;
      placeholders: {
        invoiceNumber: string;
        selectCurrency: string;
        selectIssuer: string;
        selectRecipient: string;
        selectBankDetails: string;
        searchIssuers: string;
        searchBrands: string;
        searchBankDetails: string;
        notes: string;
        itemDescription: string;
        recipientEmail: string;
        selectRecipientType: string;
        selectIssueDate: string;
        selectDueDate: string;
      };
      validation: {
        issuerRequired: string;
        recipientRequired: string;
        bankDetailsOptional: string;
        invoiceNumberRequired: string;
        invoiceNumberTooLong: string;
        currencyRequired: string;
        issueDateRequired: string;
        dueDateRequired: string;
        dueDateAfterIssue: string;
        notesTooLong: string;
        itemsRequired: string;
        itemDescriptionRequired: string;
        itemDescriptionTooLong: string;
        itemQuantityRequired: string;
        itemQuantityMin: string;
        itemUnitPriceRequired: string;
        itemUnitPriceMin: string;
        itemVatRateRequired: string;
        itemVatRateMin: string;
        itemVatRateMax: string;
        recipientsRequired: string;
        emailRequired: string;
        emailInvalid: string;
        emailTooLong: string;
        recipientTypeRequired: string;
      };
    };

    // Delete confirmation
    confirmDelete: string;
    deleteDescription: string;

    // Pagination
    page: string;
    of: string;
    previous: string;
    next: string;

    // Legacy validation (keeping for backward compatibility)
    validation: {
      invoiceNumberRequired: string;
      issueDateRequired: string;
      dueDateRequired: string;
      currencyRequired: string;
      itemsRequired: string;
      recipientsRequired: string;
      descriptionRequired: string;
      quantityRequired: string;
      quantityMin: string;
      unitPriceRequired: string;
      unitPriceMin: string;
      vatRateMin: string;
      vatRateMax: string;
      emailRequired: string;
      emailInvalid: string;
      recipientTypeRequired: string;
    };
  };
  brands: {
    title: string;
    description: string;
    createNew: string;
    noBrands: string;
    noBrandsDescription: string;
    loading: string;
    error: string;
    edit: string;
    delete: string;
    confirmDelete: string;
    deleteDescription: string;
    deleting: string;
    creating: string;
    updating: string;
    createTitle: string;
    editTitle: string;
    save: string;
    cancel: string;
    name: string;
    companyName: string;
    email: string;
    phone: string;
    website: string;
    basicInfo: string;
    addressInfo: string;
    businessInfo: string;
    addressStreet: string;
    addressCity: string;
    addressPostalCode: string;
    addressCountry: string;
    vatNumber: string;
    registrationNumber: string;
    contacts: string;
    contact: string;
    contactName: string;
    contactEmail: string;
    contactNotes: string;
    addContact: string;
    noContacts: string;
    noContactsDescription: string;
    required: string;
    searchPlaceholder: string;
    filterByName: string;
    showingResults: string;
    noBrandsForSearch: string;
    noBrandsForSearchDescription: string;
    page: string;
    of: string;
    previous: string;
    next: string;
    validation: {
      nameRequired: string;
      nameTooLong: string;
      companyNameRequired: string;
      companyNameTooLong: string;
      emailInvalid: string;
      emailTooLong: string;
      phoneTooLong: string;
      websiteTooLong: string;
      addressStreetTooLong: string;
      addressCityTooLong: string;
      addressPostalCodeTooLong: string;
      addressCountryTooLong: string;
      vatNumberTooLong: string;
      registrationNumberTooLong: string;
      contactNameRequired: string;
      contactNameTooLong: string;
      contactEmailRequired: string;
      contactEmailInvalid: string;
      contactEmailTooLong: string;
      contactNotesTooLong: string;
    };
  };
  collaborationHubs: {
    title: string;
    description: string;
    createNew: string;
    noHubs: string;
    noHubsDescription: string;
    noHubsForSearch: string;
    noHubsForSearchDescription: string;
    loading: string;
    error: string;
    viewHub: string;
    editHub: string;
    deleteHub: string;
    confirmDelete: string;
    deleteDescription: string;
    deleting: string;
    delete: string;
    cancel: string;
    hubToDelete: string;
    deleteWarning: string;
    deleteWarningDetails: string;
    hubName: string;
    brandName: string;
    myRole: string;
    createdAt: string;
    searchPlaceholder: string;
    filterByName: string;
    showingResults: string;
    page: string;
    of: string;
    previous: string;
    next: string;
    roles: {
      admin: string;
      content_creator: string;
      reviewer: string;
      reviewer_creator: string;
    };
    invite: string;
    unknown: string;
    participant: string;
    participants: string;
    created: string;
    updating: string;
    updateSuccess: string;
    updateError: string;
    open: string;
    createDialog: {
      title: string;
      description: string;
      hubName: string;
      brand: string;
      selectBrand: string;
      hubNameLabel: string;
      hubNamePlaceholder: string;
      brandLabel: string;
      brandPlaceholder: string;
      descriptionLabel: string;
      descriptionPlaceholder: string;
      cancel: string;
      creating: string;
      createHub: string;
      successMessage: string;
      errorMessage: string;
      validation: {
        hubNameRequired: string;
        hubNameTooLong: string;
        brandRequired: string;
        descriptionTooLong: string;
      };
    };
    editDialog: {
      title: string;
      description: string;
      updating: string;
      updateHub: string;
      successMessage: string;
      errorMessage: string;
    };
    tabs: {
      posts: string;
      chat: string;
      briefs: string;
      overview: string;
    };
    posts: {
      title: string;
      searchPlaceholder: string;
      filterByStatus: string;
      allPosts: string;
      pendingReview: string;
      approved: string;
      needsRework: string;
      createPost: string;
      editPost: string;
      deletePost: string;
      likes: string;
      reviewers: string;
      pending: string;
      rework: string;
      noPosts: string;
      noPostsDescription: string;
      createFirstPost: string;
      loading: string;
      loadingMore: string;
      error: string;
      errorDescription: string;
      retry: string;
      createdBy: string;
      createdAt: string;
      updatedAt: string;
      status: {
        pending: string;
        approved: string;
        rework: string;
        all: string;
      };
      filters: {
        types: {
          all: string;
          assignedToMe: string;
          needsReview: string;
          myPending: string;
          myApproved: string;
          myRework: string;
          reviewedByMe: string;
        };
      };
      actions: {
        edit: string;
        delete: string;
        view: string;
      };
      confirmDelete: string;
      deleteDescription: string;
      deleting: string;
      postDeleted: string;
      failedToDelete: string;
      postCreated: string;
      postUpdated: string;
      createPostDescription: string;
      editPostDescription: string;
      form: {
        media: string;
        mediaDescription: string;
        caption: string;
        captionPlaceholder: string;
        captionDescription: string;
        reviewerNotes: string;
        reviewerNotesPlaceholder: string;
        reviewerNotesDescription: string;
        reviewers: string;
        selectReviewers: string;
        reviewersDescription: string;
        assignReviewers: string;
        assignReviewersDescription: string;
        cancel: string;
        save: string;
        creating: string;
        updating: string;
      };
      review: {
        reviewPost: string;
        updateReview: string;
        reviewDecision: string;
        approve: string;
        requestRework: string;
        reviewNotes: string;
        notesFromCreator: string;
        notesOptional: string;
        notesRequiredForRework: string;
        notesPlaceholderApprove: string;
        notesPlaceholderRework: string;
        submitReview: string;
        submitting: string;
        reviewSubmitted: string;
        reviewFailed: string;
        statusPending: string;
        statusApproved: string;
        statusRework: string;
      };
      comments: {
        title: string;
        noComments: string;
        beFirstToComment: string;
        writeComment: string;
        postComment: string;
        posting: string;
        editComment: string;
        deleteComment: string;
        confirmDeleteComment: string;
        deleteCommentDescription: string;
        deleting: string;
        commentDeleted: string;
        commentPosted: string;
        commentUpdated: string;
        loadMore: string;
        loading: string;
        loadingComments: string;
        failedToLoad: string;
        tryAgain: string;
        comment: string;
        commentsPlural: string;
        edited: string;
        save: string;
        cancel: string;
        saving: string;
        ctrlEnterToPost: string;
        edit: string;
        delete: string;
        failedToPost: string;
        failedToUpdate: string;
        failedToDelete: string;
      };
    };
    chat: {
      channels: string;
      participants: string;
      viewMembers: string;
      messagePlaceholder: string;
      send: string;
      general: string;
      adminsOnly: string;
      contentReview: string;
      workspace: string;
      loading: string;
      error: string;
      noMessages: string;
      noMessagesDescription: string;
      typing: string;
      edited: string;
      editMessage: string;
      deleteMessage: string;
      confirmDelete: string;
      deleteDescription: string;
      uploading: string;
      uploadError: string;
      attachFile: string;
      addEmoji: string;
      mentionSomeone: string;
      sendingMessage: string;
      messageError: string;
      retryMessage: string;
      loadMore: string;
      loadingMessages: string;
      connectionError: string;
      reconnecting: string;
      connected: string;
      disconnected: string;
      members: string;
      failedToLoadMembers: string;
      noMembers: string;
      createChannel: string;
      createCustomChannel: string;
      channelName: string;
      channelDescription: string;
      selectParticipants: string;
      creating: string;
      create: string;
      editChannel: string;
      deleteChannel: string;
      manageParticipants: string;
      channelSettings: string;
      confirmDeleteChannel: string;
      deleteChannelDescription: string;
      channelNamePlaceholder: string;
      channelDescriptionPlaceholder: string;
      noParticipantsSelected: string;
      participantsSelected: string;
      addParticipants: string;
      removeParticipants: string;
      channelCreated: string;
      channelDeleted: string;
      participantsAdded: string;
      participantsRemoved: string;
      failedToCreateChannel: string;
      failedToDeleteChannel: string;
      failedToUpdateParticipants: string;
      customChannel: string;
      generalChannel: string;
      onlyCreatorCanDelete: string;
      cannotDeleteGeneral: string;
    };
    briefs: {
      searchPlaceholder: string;
      filterByAccess: string;
      allBriefs: string;
      allParticipants: string;
      contentCreators: string;
      reviewersAndAdmins: string;
      adminsOnly: string;
      createBrief: string;
      noBriefs: string;
      noBriefsDescription: string;
      createFirstBrief: string;
      updated: string;
      created: string;
      accessLevels: {
        all: string;
        creators: string;
        reviewers: string;
        admins: string;
      };
      dialog: {
        createTitle: string;
        editTitle: string;
        titleLabel: string;
        titlePlaceholder: string;
        bodyLabel: string;
        bodyPlaceholder: string;
        scopeLabel: string;
        scopeDescription: string;
        specificPeopleLabel: string;
        specificPeopleDescription: string;
        cancel: string;
        save: string;
        creating: string;
        updating: string;
        createSuccess: string;
        updateSuccess: string;
      };
      viewDialog: {
        title: string;
        loading: string;
        error: string;
        close: string;
        basicInfo: string;
        content: string;
        creatorInfo: string;
        createdBy: string;
        createdAt: string;
        updatedAt: string;
        noContent: string;
      };
      scopes: {
        allParticipants: string;
        adminsReviewers: string;
        adminsOnly: string;
        customSelection: string;
      };
      validation: {
        titleRequired: string;
        titleMaxLength: string;
        bodyMaxLength: string;
      };
      actions: {
        edit: string;
        delete: string;
        confirmDelete: string;
        confirmDeleteDescription: string;
        deleteSuccess: string;
      };
    };
    overview: {
      totalPosts: string;
      pendingReviews: string;
      approved: string;
      participants: string;
      approvalRate: string;
      contentApproval: string;
      approvedOutOf: string;
      participationRate: string;
      activeParticipants: string;
      activeOutOf: string;
      recentActivity: string;
      messagesSent: string;
      briefsCreated: string;
      needsRework: string;
      justNow: string;
      hoursAgo: string;
      daysAgo: string;
      activities: {
        created: string;
        approved: string;
        commentedOn: string;
        joinedHub: string;
        updated: string;
      };
    };
    mediaCarousel: {
      video: string;
      mediaAlt: string;
    };
    manageParticipants: {
      title: string;
      description: string;
      inviteTab: string;
      manageTab: string;
      participantType: string;
      externalUser: string;
      brandContact: string;
      emailAddress: string;
      name: string;
      nameOptional: string;
      selectContact: string;
      loadingContacts: string;
      searchContacts: string;
      noContactsFound: string;
      noContactsForBrand: string;
      role: string;
      selectRole: string;
      externalInviteInfo: string;
      brandContactInviteInfo: string;
      sendInvitation: string;
      sendingInvitation: string;
      cancel: string;
      close: string;
      noParticipants: string;
      noParticipantsDescription: string;
      inviteParticipants: string;
      loadingParticipants: string;
      external: string;
      makeAdmin: string;
      contentCreator: string;
      reviewer: string;
      reviewerCreator: string;
      resendInvitation: string;
      remove: string;
      managingCount: string;
    };
  };
  common: {
    loading: string;
    cancel: string;
    save: string;
    delete: string;
    edit: string;
    create: string;
    update: string;
    retry: string;
    deleting: string;
    close: string;
    clearAll: string;
  };
  ui: {
    emojiPicker: {
      addEmoji: string;
      categories: {
        smileysAndPeople: string;
        animalsAndNature: string;
        foodAndDrink: string;
        activities: string;
        travelAndPlaces: string;
        objects: string;
        symbols: string;
      };
    };
    textareaWithEmoji: {
      emojiInserted: string;
    };
    reviewerMultiSelect: {
      selectReviewers: string;
      searchReviewers: string;
      loadingReviewers: string;
      noReviewersFound: string;
      noReviewersAvailable: string;
      reviewersSelected: string;
      reviewer: string;
      reviewers: string;
    };
    mentionInput: {
      loadingParticipants: string;
      noParticipantsFound: string;
    };
    userProfilePopup: {
      unknownUser: string;
      externalUser: string;
      sendEmail: string;
    };
  };
  navigation: {
    userProfile: {
      logout: string;
      loggingOut: string;
    };
  };
}

// Declare module for react-i18next to provide type safety
declare module 'react-i18next' {
  interface CustomTypeOptions {
    defaultNS: 'translation';
    resources: {
      translation: TranslationResources;
    };
  }
}
